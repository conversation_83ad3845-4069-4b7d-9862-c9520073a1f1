# Documentation API Pressing

## Vue d'ensemble

Cette API REST permet de gérer un pressing avec les fonctionnalités suivantes :
- Gestion des clients
- Gestion des commandes
- Suivi des statuts
- Tableau de bord avec statistiques

**URL de base :** `http://localhost:8080`
**Documentation Swagger :** `http://localhost:8080/swagger-ui.html`

## Authentification

Actuellement, l'API ne nécessite pas d'authentification.

## Endpoints

### 1. Gestion des Clients

#### Créer un client
```http
POST /api/clients
Content-Type: application/json

{
  "nom": "<PERSON>",
  "telephone": "0123456789",
  "adresse": "123 Rue de la Paix, Paris"
}
```

#### Récupérer tous les clients
```http
GET /api/clients
```

#### Récupérer un client par ID
```http
GET /api/clients/{id}
```

#### Rechercher des clients par nom
```http
GET /api/clients/search?nom=Jean
```

#### Récupérer un client par téléphone
```http
GET /api/clients/phone/{telephone}
```

#### Mettre à jour un client
```http
PUT /api/clients/{id}
Content-Type: application/json

{
  "nom": "Jean Dupont",
  "telephone": "0123456789",
  "adresse": "456 Avenue des Champs, Paris"
}
```

#### Supprimer un client
```http
DELETE /api/clients/{id}
```

### 2. Gestion des Commandes

#### Créer une commande
```http
POST /api/commandes
Content-Type: application/json

{
  "clientId": 1,
  "lignes": [
    {
      "article": "CHEMISE",
      "quantite": 2
    },
    {
      "article": "PANTALON",
      "quantite": 1
    }
  ],
  "notes": "Livraison urgente"
}
```

#### Récupérer toutes les commandes
```http
GET /api/commandes
```

#### Récupérer une commande par ID
```http
GET /api/commandes/{id}
```

#### Récupérer les commandes d'un client
```http
GET /api/commandes/client/{clientId}
```

#### Récupérer les commandes par statut
```http
GET /api/commandes/statut/{statut}
```

Statuts disponibles : `EN_ATTENTE`, `EN_COURS`, `TERMINEE`, `LIVREE`

#### Mettre à jour le statut d'une commande
```http
PATCH /api/commandes/{commandeId}/statut
Content-Type: application/json

{
  "statut": "EN_COURS"
}
```

#### Récupérer les commandes entre deux dates
```http
GET /api/commandes/periode?start=2024-01-01T00:00:00&end=2024-12-31T23:59:59
```

#### Supprimer une commande
```http
DELETE /api/commandes/{id}
```

### 3. Types d'Articles

#### Récupérer tous les types d'articles
```http
GET /api/articles
```

Retourne la liste des articles avec leurs prix :
```json
[
  {
    "type": "CHEMISE",
    "libelle": "Chemise",
    "prix": 15.0
  },
  {
    "type": "PANTALON",
    "libelle": "Pantalon",
    "prix": 20.0
  }
]
```

### 4. Tableau de Bord

#### Récupérer les statistiques
```http
GET /api/dashboard/stats
```

Retourne :
```json
{
  "commandesAujourdhui": 5,
  "chiffreAffairesJour": 150.0,
  "enAttente": 3,
  "enCours": 2,
  "terminees": 8
}
```

## Modèles de Données

### Client
```json
{
  "id": 1,
  "nom": "Jean Dupont",
  "telephone": "0123456789",
  "adresse": "123 Rue de la Paix, Paris"
}
```

### Commande
```json
{
  "id": 1,
  "client": {
    "id": 1,
    "nom": "Jean Dupont",
    "telephone": "0123456789",
    "adresse": "123 Rue de la Paix, Paris"
  },
  "dateCommande": "2024-01-15T10:30:00",
  "lignes": [
    {
      "id": 1,
      "article": "CHEMISE",
      "quantite": 2,
      "prixUnitaire": 15.0,
      "sousTotal": 30.0
    }
  ],
  "total": 30.0,
  "statut": "EN_ATTENTE",
  "notes": "Livraison urgente"
}
```

### Types d'Articles Disponibles
- `CHEMISE` - 15.0€
- `PANTALON` - 20.0€
- `ROBE` - 25.0€
- `VESTE` - 30.0€
- `COSTUME` - 40.0€
- `CHAUSSURE` - 15.0€
- `DRAP` - 20.0€
- `COUVERTURE` - 25.0€

## Codes de Réponse HTTP

- `200 OK` - Succès
- `201 Created` - Ressource créée avec succès
- `204 No Content` - Suppression réussie
- `400 Bad Request` - Erreur de validation
- `404 Not Found` - Ressource non trouvée
- `500 Internal Server Error` - Erreur serveur

## Gestion des Erreurs

Les erreurs retournent un objet JSON standardisé :
```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": 400,
  "error": "Validation Error",
  "message": "Le nom est obligatoire",
  "path": "/api/clients",
  "details": [
    "nom: Le nom est obligatoire"
  ]
}
```

## Configuration CORS

L'API est configurée pour accepter les requêtes de toutes les origines en développement.

## Démarrage de l'Application

1. Assurez-vous que PostgreSQL est installé et en cours d'exécution
2. Créez une base de données nommée `pressingg`
3. Configurez les paramètres de connexion dans `application.properties`
4. Exécutez : `mvn spring-boot:run`
5. L'API sera disponible sur `http://localhost:8080`
6. La documentation Swagger sera disponible sur `http://localhost:8080/swagger-ui.html`
