# Guide de Démarrage Rapide - API Pressing

## Prérequis

1. **Java 17** ou supérieur
2. **PostgreSQL** installé et en cours d'exécution
3. **Git** (pour cloner le projet)

## Installation

### 1. Préparer la base de données

Connectez-vous à PostgreSQL et créez la base de données :

```sql
-- Se connecter à PostgreSQL
psql -U postgres

-- Créer la base de données
CREATE DATABASE pressingg;

-- Vérifier la création
\l
```

### 2. Configurer l'application

Le fichier `src/main/resources/application.properties` contient la configuration par défaut :

```properties
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=2001
```

**Modifiez le mot de passe** selon votre configuration PostgreSQL.

### 3. Compiler et démarrer l'application

```bash
# Compiler le projet
./mvnw clean compile

# Démarrer l'application
./mvnw spring-boot:run
```

L'application sera disponible sur : `http://localhost:8080`

## Vérification du démarrage

### 1. Vérifier que l'API fonctionne

```bash
# Test simple
curl http://localhost:8080/api/articles
```

### 2. Accéder à la documentation Swagger

Ouvrez votre navigateur et allez sur :
`http://localhost:8080/swagger-ui.html`

## Premiers tests

### 1. Créer un client

```bash
curl -X POST http://localhost:8080/api/clients \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Jean Dupont",
    "telephone": "0123456789",
    "adresse": "123 Rue de la Paix, Paris"
  }'
```

### 2. Lister les clients

```bash
curl http://localhost:8080/api/clients
```

### 3. Voir les types d'articles disponibles

```bash
curl http://localhost:8080/api/articles
```

### 4. Créer une commande

```bash
curl -X POST http://localhost:8080/api/commandes \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": 1,
    "lignes": [
      {
        "article": "CHEMISE",
        "quantite": 2
      },
      {
        "article": "PANTALON",
        "quantite": 1
      }
    ],
    "notes": "Livraison urgente"
  }'
```

### 5. Voir les statistiques du dashboard

```bash
curl http://localhost:8080/api/dashboard/stats
```

## Résolution des problèmes courants

### Erreur de connexion à la base de données

```
org.postgresql.util.PSQLException: Connection refused
```

**Solutions :**
1. Vérifiez que PostgreSQL est démarré
2. Vérifiez l'URL, le nom d'utilisateur et le mot de passe dans `application.properties`
3. Vérifiez que la base de données `pressingg` existe

### Port 8080 déjà utilisé

```
Port 8080 was already in use
```

**Solutions :**
1. Arrêtez l'application qui utilise le port 8080
2. Ou changez le port dans `application.properties` :
   ```properties
   server.port=8081
   ```

### Erreur de compilation Java

```
Error: JAVA_HOME is not defined correctly
```

**Solutions :**
1. Installez Java 17
2. Définissez JAVA_HOME :
   ```bash
   export JAVA_HOME=/path/to/java17
   ```

## Utilisation avec un frontend

### Configuration CORS

L'API est configurée pour accepter les requêtes de toutes les origines en développement.

### Endpoints principaux pour le frontend

- **Clients** : `/api/clients`
- **Commandes** : `/api/commandes`
- **Articles** : `/api/articles`
- **Dashboard** : `/api/dashboard/stats`

### Exemple d'intégration JavaScript

```javascript
// Configuration de base
const API_BASE_URL = 'http://localhost:8080/api';

// Récupérer tous les clients
async function getClients() {
  const response = await fetch(`${API_BASE_URL}/clients`);
  return response.json();
}

// Créer un client
async function createClient(clientData) {
  const response = await fetch(`${API_BASE_URL}/clients`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(clientData)
  });
  return response.json();
}

// Créer une commande
async function createCommande(commandeData) {
  const response = await fetch(`${API_BASE_URL}/commandes`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(commandeData)
  });
  return response.json();
}
```

## Prochaines étapes

1. Consultez la [documentation complète de l'API](API_DOCUMENTATION.md)
2. Explorez l'interface Swagger pour tester tous les endpoints
3. Intégrez l'API avec votre frontend
4. Personnalisez la configuration selon vos besoins

## Support

Pour toute question ou problème :
1. Consultez les logs de l'application
2. Vérifiez la documentation Swagger
3. Consultez le fichier README.md pour plus de détails
