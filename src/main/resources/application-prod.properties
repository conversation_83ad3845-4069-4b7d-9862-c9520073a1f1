# Configuration pour l'environnement de production

# Base de données (à configurer selon votre environnement)
spring.datasource.url=${DATABASE_URL:******************************************}
spring.datasource.username=${DATABASE_USERNAME:postgres}
spring.datasource.password=${DATABASE_PASSWORD:changeme}

# JPA/Hibernate
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Logs pour la production
logging.level.com.example.pressing=INFO
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN

# Sécurité
server.error.include-stacktrace=never
server.error.include-message=never

# CORS restrictif pour la production
cors.allowed-origins=${ALLOWED_ORIGINS:http://localhost:3000,https://yourdomain.com}
