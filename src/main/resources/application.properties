# Configuration de la base de donnees
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=2001
spring.datasource.driver-class-name=org.postgresql.Driver

# Configuration JPA/Hibernate
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Configuration du serveur
server.port=8080
server.servlet.context-path=/

# Configuration Swagger/OpenAPI
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Configuration des logs
logging.level.com.example.pressing=DEBUG
logging.level.org.springframework.web=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Configuration de validation
spring.jackson.deserialization.fail-on-unknown-properties=false
