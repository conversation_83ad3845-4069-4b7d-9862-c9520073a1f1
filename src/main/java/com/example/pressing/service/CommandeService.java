package com.example.pressing.service;

import com.example.pressing.entity.*;
import com.example.pressing.enumeration.ArticleType;
import com.example.pressing.exception.ResourceNotFoundException;
import com.example.pressing.exception.ValidationException;
import com.example.pressing.repository.CommandeRepository;
import com.example.pressing.repository.LigneCommandeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CommandeService {
    private final CommandeRepository commandeRepository;
    private final LigneCommandeRepository ligneCommandeRepository;
    private final ClientService clientService;

    @Transactional
    public Commande createCommande(Long clientId, Map<ArticleType, Integer> articles, String notes) {
        if (articles == null || articles.isEmpty()) {
            throw new ValidationException("Au moins un article est requis pour créer une commande");
        }

        Client client = clientService.getClientById(clientId);

        Commande commande = Commande.builder()
                .client(client)
                .notes(notes)
                .build();

        commande = commandeRepository.save(commande);

        double total = 0;
        for (Map.Entry<ArticleType, Integer> entry : articles.entrySet()) {
            if (entry.getValue() <= 0) {
                throw new ValidationException("La quantité doit être positive pour l'article " + entry.getKey().getLibelle());
            }

            LigneCommande ligne = LigneCommande.builder()
                    .article(entry.getKey())
                    .quantite(entry.getValue())
                    .prixUnitaire(entry.getKey().getPrix())
                    .commande(commande)
                    .build();

            ligneCommandeRepository.save(ligne);
            total += entry.getKey().getPrix() * entry.getValue();
        }

        commande.setTotal(total);
        return commandeRepository.save(commande);
    }

    public List<Commande> getCommandesByClient(Long clientId) {
        return commandeRepository.findByClientId(clientId);
    }

    public List<Commande> getCommandesByStatut(String statut) {
        return commandeRepository.findByStatut(statut);
    }

    public Commande updateStatut(Long commandeId, String nouveauStatut) {
        Commande commande = commandeRepository.findById(commandeId)
                .orElseThrow(() -> new ResourceNotFoundException("Commande", "id", commandeId));

        // Valider le statut
        if (!isValidStatut(nouveauStatut)) {
            throw new ValidationException("Statut invalide: " + nouveauStatut);
        }

        commande.setStatut(nouveauStatut);
        return commandeRepository.save(commande);
    }

    private boolean isValidStatut(String statut) {
        return statut != null && (statut.equals("EN_ATTENTE") || statut.equals("EN_COURS")
                || statut.equals("TERMINEE") || statut.equals("LIVREE"));
    }

    public Commande getCommandeById(Long id) {
        return commandeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Commande", "id", id));
    }

    public List<Commande> getAllCommandes() {
        return commandeRepository.findAll();
    }

    @Transactional
    public void deleteCommande(Long id) {
        Commande commande = getCommandeById(id);
        commandeRepository.delete(commande);
    }

    public List<Commande> getCommandesBetweenDates(LocalDateTime start, LocalDateTime end) {
        return commandeRepository.findBetweenDates(start, end);
    }
}