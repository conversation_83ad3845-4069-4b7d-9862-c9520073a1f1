package com.example.pressing.service;

import com.example.pressing.repository.CommandeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class DashboardService {
    private final CommandeRepository commandeRepository;

    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();

        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        // Commandes du jour
        stats.put("commandesAujourdhui", commandeRepository.countByDateCommandeBetween(startOfDay, endOfDay));

        // Chiffre d'affaires du jour
        Double caJour = commandeRepository.sumTotalByDateCommandeBetween(startOfDay, endOfDay);
        stats.put("chiffreAffairesJour", caJour != null ? caJour : 0);

        // Commandes par statut
        stats.put("enAttente", commandeRepository.countByStatut("EN_ATTENTE"));
        stats.put("enCours", commandeRepository.countByStatut("EN_COURS"));
        stats.put("terminees", commandeRepository.countByStatut("TERMINEE"));

        return stats;
    }
}