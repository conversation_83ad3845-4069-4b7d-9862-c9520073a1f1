package com.example.pressing.service;

import com.example.pressing.entity.Client;
import com.example.pressing.exception.ResourceNotFoundException;
import com.example.pressing.exception.ValidationException;
import com.example.pressing.repository.ClientRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ClientService {
    private final ClientRepository clientRepository;

    public Client createClient(Client client) {
        // Vérifier si le téléphone existe déjà
        if (clientRepository.findByTelephone(client.getTelephone()) != null) {
            throw new ValidationException("Un client avec ce numéro de téléphone existe déjà");
        }
        return clientRepository.save(client);
    }

    public Client getClientById(Long id) {
        return clientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Client", "id", id));
    }

    public List<Client> searchClients(String nom) {
        return clientRepository.findByNomContainingIgnoreCase(nom);
    }

    public Client getClientByPhone(String telephone) {
        Client client = clientRepository.findByTelephone(telephone);
        if (client == null) {
            throw new ResourceNotFoundException("Client", "téléphone", telephone);
        }
        return client;
    }

    public List<Client> getAllClients() {
        return clientRepository.findAll();
    }

    public Client updateClient(Long id, Client clientDetails) {
        Client client = getClientById(id);

        // Vérifier si le nouveau téléphone n'est pas déjà utilisé par un autre client
        if (!client.getTelephone().equals(clientDetails.getTelephone())) {
            Client existingClient = clientRepository.findByTelephone(clientDetails.getTelephone());
            if (existingClient != null && !existingClient.getId().equals(id)) {
                throw new ValidationException("Un autre client utilise déjà ce numéro de téléphone");
            }
        }

        client.setNom(clientDetails.getNom());
        client.setTelephone(clientDetails.getTelephone());
        client.setAdresse(clientDetails.getAdresse());

        return clientRepository.save(client);
    }

    public void deleteClient(Long id) {
        Client client = getClientById(id);
        clientRepository.delete(client);
    }
}