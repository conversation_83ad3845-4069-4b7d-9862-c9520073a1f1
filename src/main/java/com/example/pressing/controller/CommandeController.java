package com.example.pressing.controller;

import com.example.pressing.dto.CommandeDTO;
import com.example.pressing.dto.CreateCommandeDTO;
import com.example.pressing.dto.UpdateStatutDTO;
import com.example.pressing.entity.Commande;
import com.example.pressing.entity.LigneCommande;
import com.example.pressing.enumeration.ArticleType;
import com.example.pressing.mapper.CommandeMapper;
import com.example.pressing.service.CommandeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/commandes")
@RequiredArgsConstructor
@Tag(name = "Commandes", description = "API de gestion des commandes")
public class CommandeController {
    private final CommandeService commandeService;
    private final CommandeMapper commandeMapper;

    @PostMapping
    @Operation(summary = "Créer une nouvelle commande")
    public ResponseEntity<CommandeDTO> createCommande(@Valid @RequestBody CreateCommandeDTO createCommandeDTO) {
        // Convertir les lignes en Map pour le service existant
        Map<ArticleType, Integer> articles = new HashMap<>();
        createCommandeDTO.getLignes().forEach(ligne ->
            articles.put(ligne.getArticle(), ligne.getQuantite()));

        Commande commande = commandeService.createCommande(
            createCommandeDTO.getClientId(),
            articles,
            createCommandeDTO.getNotes()
        );
        return new ResponseEntity<>(commandeMapper.toDTO(commande), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Récupérer une commande par son ID")
    public ResponseEntity<CommandeDTO> getCommande(
            @Parameter(description = "ID de la commande") @PathVariable Long id) {
        Commande commande = commandeService.getCommandeById(id);
        return ResponseEntity.ok(commandeMapper.toDTO(commande));
    }

    @GetMapping
    @Operation(summary = "Récupérer toutes les commandes")
    public ResponseEntity<List<CommandeDTO>> getAllCommandes() {
        List<Commande> commandes = commandeService.getAllCommandes();
        List<CommandeDTO> commandeDTOs = commandes.stream()
                .map(commandeMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(commandeDTOs);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer une commande")
    public ResponseEntity<Void> deleteCommande(
            @Parameter(description = "ID de la commande") @PathVariable Long id) {
        commandeService.deleteCommande(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Récupérer les commandes d'un client")
    public ResponseEntity<List<CommandeDTO>> getCommandesByClient(
            @Parameter(description = "ID du client") @PathVariable Long clientId) {
        List<Commande> commandes = commandeService.getCommandesByClient(clientId);
        List<CommandeDTO> commandeDTOs = commandes.stream()
                .map(commandeMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(commandeDTOs);
    }

    @GetMapping("/statut/{statut}")
    @Operation(summary = "Récupérer les commandes par statut")
    public ResponseEntity<List<CommandeDTO>> getCommandesByStatut(
            @Parameter(description = "Statut des commandes") @PathVariable String statut) {
        List<Commande> commandes = commandeService.getCommandesByStatut(statut);
        List<CommandeDTO> commandeDTOs = commandes.stream()
                .map(commandeMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(commandeDTOs);
    }

    @PatchMapping("/{commandeId}/statut")
    @Operation(summary = "Mettre à jour le statut d'une commande")
    public ResponseEntity<CommandeDTO> updateStatut(
            @Parameter(description = "ID de la commande") @PathVariable Long commandeId,
            @Valid @RequestBody UpdateStatutDTO updateStatutDTO) {
        Commande commande = commandeService.updateStatut(commandeId, updateStatutDTO.getStatut());
        return ResponseEntity.ok(commandeMapper.toDTO(commande));
    }

    @GetMapping("/periode")
    @Operation(summary = "Récupérer les commandes entre deux dates")
    public ResponseEntity<List<CommandeDTO>> getCommandesBetweenDates(
            @Parameter(description = "Date de début")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
            @Parameter(description = "Date de fin")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end) {
        List<Commande> commandes = commandeService.getCommandesBetweenDates(start, end);
        List<CommandeDTO> commandeDTOs = commandes.stream()
                .map(commandeMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(commandeDTOs);
    }
}