package com.example.pressing.controller;

import com.example.pressing.dto.ClientDTO;
import com.example.pressing.entity.Client;
import com.example.pressing.mapper.ClientMapper;
import com.example.pressing.service.ClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/clients")
@RequiredArgsConstructor
@Tag(name = "Clients", description = "API de gestion des clients")
public class ClientController {
    private final ClientService clientService;
    private final ClientMapper clientMapper;

    @PostMapping
    @Operation(summary = "Créer un nouveau client")
    public ResponseEntity<ClientDTO> createClient(@Valid @RequestBody ClientDTO clientDTO) {
        Client client = clientMapper.toEntity(clientDTO);
        Client savedClient = clientService.createClient(client);
        return new ResponseEntity<>(clientMapper.toDTO(savedClient), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Récupérer un client par son ID")
    public ResponseEntity<ClientDTO> getClient(
            @Parameter(description = "ID du client") @PathVariable Long id) {
        Client client = clientService.getClientById(id);
        return ResponseEntity.ok(clientMapper.toDTO(client));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Mettre à jour un client")
    public ResponseEntity<ClientDTO> updateClient(
            @Parameter(description = "ID du client") @PathVariable Long id,
            @Valid @RequestBody ClientDTO clientDTO) {
        Client clientDetails = clientMapper.toEntity(clientDTO);
        Client updatedClient = clientService.updateClient(id, clientDetails);
        return ResponseEntity.ok(clientMapper.toDTO(updatedClient));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer un client")
    public ResponseEntity<Void> deleteClient(
            @Parameter(description = "ID du client") @PathVariable Long id) {
        clientService.deleteClient(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/search")
    @Operation(summary = "Rechercher des clients par nom")
    public ResponseEntity<List<ClientDTO>> searchClients(
            @Parameter(description = "Nom à rechercher") @RequestParam String nom) {
        List<Client> clients = clientService.searchClients(nom);
        List<ClientDTO> clientDTOs = clients.stream()
                .map(clientMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(clientDTOs);
    }

    @GetMapping("/phone/{telephone}")
    @Operation(summary = "Récupérer un client par son numéro de téléphone")
    public ResponseEntity<ClientDTO> getClientByPhone(
            @Parameter(description = "Numéro de téléphone") @PathVariable String telephone) {
        Client client = clientService.getClientByPhone(telephone);
        return ResponseEntity.ok(clientMapper.toDTO(client));
    }

    @GetMapping
    @Operation(summary = "Récupérer tous les clients")
    public ResponseEntity<List<ClientDTO>> getAllClients() {
        List<Client> clients = clientService.getAllClients();
        List<ClientDTO> clientDTOs = clients.stream()
                .map(clientMapper::toDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(clientDTOs);
    }
}