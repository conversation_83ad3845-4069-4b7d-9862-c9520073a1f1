package com.example.pressing.controller;

import com.example.pressing.enumeration.ArticleType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/articles")
@Tag(name = "Articles", description = "API de gestion des types d'articles")
public class ArticleController {

    @GetMapping
    @Operation(summary = "Récupérer tous les types d'articles avec leurs prix")
    public ResponseEntity<List<Map<String, Object>>> getAllArticleTypes() {
        List<Map<String, Object>> articles = Arrays.stream(ArticleType.values())
                .map(article -> {
                    Map<String, Object> articleMap = new HashMap<>();
                    articleMap.put("type", article.name());
                    articleMap.put("libelle", article.getLibelle());
                    articleMap.put("prix", article.getPrix());
                    return articleMap;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(articles);
    }
}
