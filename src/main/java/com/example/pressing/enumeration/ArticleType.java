package com.example.pressing.enumeration;

public enum ArticleType {
    CHEMISE(15.0, "Chemise"),
    PANTALON(20.0, "Pantalon"),
    ROBE(25.0, "Robe"),
    VESTE(30.0, "Veste"),
    COSTUME(40.0, "Costume"),
    CHAUSSURE(15.0, "<PERSON><PERSON>ure"),
    DRAP(20.0, "Drap"),
    COUVERTURE(25.0, "Couverture");

    private final double prix;
    private final String libelle;

    ArticleType(double prix, String libelle) {
        this.prix = prix;
        this.libelle = libelle;
    }

    public double getPrix() {
        return prix;
    }

    public String getLibelle() {
        return libelle;
    }
}