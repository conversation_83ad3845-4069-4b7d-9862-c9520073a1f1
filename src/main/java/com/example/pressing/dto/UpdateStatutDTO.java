package com.example.pressing.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateStatutDTO {
    @NotBlank(message = "Le statut est obligatoire")
    @Pattern(regexp = "EN_ATTENTE|EN_COURS|TERMINEE|LIVREE", 
             message = "Statut invalide. Valeurs autorisées: EN_ATTENTE, EN_COURS, TERMINEE, LIVREE")
    private String statut;
}
