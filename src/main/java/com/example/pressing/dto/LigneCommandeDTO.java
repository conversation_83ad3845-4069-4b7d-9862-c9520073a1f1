package com.example.pressing.dto;

import com.example.pressing.enumeration.ArticleType;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LigneCommandeDTO {
    private Long id;
    
    @NotNull(message = "Le type d'article est obligatoire")
    private ArticleType article;
    
    @Min(value = 1, message = "La quantité doit être au moins 1")
    private int quantite;
    
    private double prixUnitaire;
    private double sousTotal;
}
