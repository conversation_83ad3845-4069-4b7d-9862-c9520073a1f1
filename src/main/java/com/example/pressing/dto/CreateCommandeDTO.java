package com.example.pressing.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateCommandeDTO {
    @NotNull(message = "L'ID du client est obligatoire")
    private Long clientId;
    
    @NotEmpty(message = "Au moins un article est requis")
    @Valid
    private List<LigneCommandeDTO> lignes;
    
    private String notes;
}
