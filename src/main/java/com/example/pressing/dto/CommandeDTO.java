package com.example.pressing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommandeDTO {
    private Long id;
    private ClientDTO client;
    private LocalDateTime dateCommande;
    private List<LigneCommandeDTO> lignes;
    private double total;
    private String statut;
    private String notes;
}
