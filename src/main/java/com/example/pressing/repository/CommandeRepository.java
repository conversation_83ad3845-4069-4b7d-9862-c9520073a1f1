package com.example.pressing.repository;

import com.example.pressing.entity.Commande;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface CommandeRepository extends JpaRepository<Commande, Long> {
    List<Commande> findByClientId(Long clientId);
    List<Commande> findByStatut(String statut);

    @Query("SELECT c FROM Commande c WHERE c.dateCommande BETWEEN :start AND :end")
    List<Commande> findBetweenDates(@Param("start") LocalDateTime start,
                                    @Param("end") LocalDateTime end);

    // Méthode pour compter les commandes entre deux dates
    @Query("SELECT COUNT(c) FROM Commande c WHERE c.dateCommande BETWEEN :start AND :end")
    Long countByDateCommandeBetween(@Param("start") LocalDateTime start,
                                    @Param("end") LocalDateTime end);

    // Méthode pour sommer le total des commandes entre deux dates
    @Query("SELECT SUM(c.total) FROM Commande c WHERE c.dateCommande BETWEEN :start AND :end")
    Double sumTotalByDateCommandeBetween(@Param("start") LocalDateTime start,
                                         @Param("end") LocalDateTime end);

    // Méthode pour compter les commandes par statut
    Long countByStatut(String statut);
}