package com.example.pressing.mapper;

import com.example.pressing.dto.ClientDTO;
import com.example.pressing.entity.Client;
import org.springframework.stereotype.Component;

@Component
public class ClientMapper {
    
    public ClientDTO toDTO(Client client) {
        if (client == null) {
            return null;
        }
        
        return ClientDTO.builder()
                .id(client.getId())
                .nom(client.getNom())
                .telephone(client.getTelephone())
                .adresse(client.getAdresse())
                .build();
    }
    
    public Client toEntity(ClientDTO clientDTO) {
        if (clientDTO == null) {
            return null;
        }
        
        return Client.builder()
                .id(clientDTO.getId())
                .nom(clientDTO.getNom())
                .telephone(clientDTO.getTelephone())
                .adresse(clientDTO.getAdresse())
                .build();
    }
}
