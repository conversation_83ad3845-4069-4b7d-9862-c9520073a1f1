package com.example.pressing.mapper;

import com.example.pressing.dto.CommandeDTO;
import com.example.pressing.dto.LigneCommandeDTO;
import com.example.pressing.entity.Commande;
import com.example.pressing.entity.LigneCommande;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class CommandeMapper {
    
    private final ClientMapper clientMapper;
    
    public CommandeDTO toDTO(Commande commande) {
        if (commande == null) {
            return null;
        }
        
        List<LigneCommandeDTO> lignesDTO = commande.getLignes() != null 
            ? commande.getLignes().stream()
                .map(this::toLigneCommandeDTO)
                .collect(Collectors.toList())
            : null;
        
        return CommandeDTO.builder()
                .id(commande.getId())
                .client(clientMapper.toDTO(commande.getClient()))
                .dateCommande(commande.getDateCommande())
                .lignes(lignesDTO)
                .total(commande.getTotal())
                .statut(commande.getStatut())
                .notes(commande.getNotes())
                .build();
    }
    
    public LigneCommandeDTO toLigneCommandeDTO(LigneCommande ligne) {
        if (ligne == null) {
            return null;
        }
        
        return LigneCommandeDTO.builder()
                .id(ligne.getId())
                .article(ligne.getArticle())
                .quantite(ligne.getQuantite())
                .prixUnitaire(ligne.getPrixUnitaire())
                .sousTotal(ligne.getPrixUnitaire() * ligne.getQuantite())
                .build();
    }
    
    public LigneCommande toLigneCommandeEntity(LigneCommandeDTO ligneDTO) {
        if (ligneDTO == null) {
            return null;
        }
        
        return LigneCommande.builder()
                .id(ligneDTO.getId())
                .article(ligneDTO.getArticle())
                .quantite(ligneDTO.getQuantite())
                .prixUnitaire(ligneDTO.getPrixUnitaire())
                .build();
    }
}
