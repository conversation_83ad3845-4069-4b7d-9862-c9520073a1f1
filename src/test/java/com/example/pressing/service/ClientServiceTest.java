package com.example.pressing.service;

import com.example.pressing.entity.Client;
import com.example.pressing.exception.ResourceNotFoundException;
import com.example.pressing.exception.ValidationException;
import com.example.pressing.repository.ClientRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ClientServiceTest {

    @Mock
    private ClientRepository clientRepository;

    @InjectMocks
    private ClientService clientService;

    private Client client;

    @BeforeEach
    void setUp() {
        client = Client.builder()
                .id(1L)
                .nom("<PERSON>")
                .telephone("0123456789")
                .adresse("123 Rue de la Paix")
                .build();
    }

    @Test
    void createClient_Success() {
        // Given
        when(clientRepository.findByTelephone(client.getTelephone())).thenReturn(null);
        when(clientRepository.save(any(Client.class))).thenReturn(client);

        // When
        Client result = clientService.createClient(client);

        // Then
        assertNotNull(result);
        assertEquals(client.getNom(), result.getNom());
        verify(clientRepository).save(client);
    }

    @Test
    void createClient_ThrowsValidationException_WhenPhoneExists() {
        // Given
        when(clientRepository.findByTelephone(client.getTelephone())).thenReturn(client);

        // When & Then
        assertThrows(ValidationException.class, () -> clientService.createClient(client));
        verify(clientRepository, never()).save(any());
    }

    @Test
    void getClientById_Success() {
        // Given
        when(clientRepository.findById(1L)).thenReturn(Optional.of(client));

        // When
        Client result = clientService.getClientById(1L);

        // Then
        assertNotNull(result);
        assertEquals(client.getId(), result.getId());
    }

    @Test
    void getClientById_ThrowsResourceNotFoundException() {
        // Given
        when(clientRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> clientService.getClientById(1L));
    }

    @Test
    void getAllClients_Success() {
        // Given
        List<Client> clients = Arrays.asList(client);
        when(clientRepository.findAll()).thenReturn(clients);

        // When
        List<Client> result = clientService.getAllClients();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(client.getNom(), result.get(0).getNom());
    }

    @Test
    void searchClients_Success() {
        // Given
        List<Client> clients = Arrays.asList(client);
        when(clientRepository.findByNomContainingIgnoreCase("Jean")).thenReturn(clients);

        // When
        List<Client> result = clientService.searchClients("Jean");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(client.getNom(), result.get(0).getNom());
    }
}
