# API Pressing - Backend

## Description

Application backend Spring Boot pour la gestion d'un pressing. Cette API REST permet de gérer les clients, les commandes, et fournit un tableau de bord avec des statistiques.

## Fonctionnalités

- ✅ Gestion complète des clients (CRUD)
- ✅ Gestion des commandes avec lignes de commande
- ✅ Suivi des statuts de commandes
- ✅ Tableau de bord avec statistiques
- ✅ API REST documentée avec Swagger
- ✅ Validation des données
- ✅ Gestion d'erreurs centralisée
- ✅ Configuration CORS pour frontend

## Technologies Utilisées

- **Java 17**
- **Spring Boot 3.3.2**
- **Spring Data JPA**
- **PostgreSQL**
- **Lombok**
- **Swagger/OpenAPI 3**
- **Maven**

## Prérequis

- Java 17 ou supérieur
- Maven 3.6+
- PostgreSQL 12+

## Installation et Configuration

### 1. <PERSON><PERSON><PERSON> le projet
```bash
git clone <url-du-repo>
cd pressing
```

### 2. Configuration de la base de données

Créez une base de données PostgreSQL :
```sql
CREATE DATABASE pressingg;
```

Modifiez le fichier `src/main/resources/application.properties` si nécessaire :
```properties
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=2001
```

### 3. Compilation et exécution
```bash
# Compiler le projet
mvn clean compile

# Exécuter l'application
mvn spring-boot:run
```

L'application sera disponible sur `http://localhost:8080`

## Documentation API

- **Swagger UI** : `http://localhost:8080/swagger-ui.html`
- **API Docs JSON** : `http://localhost:8080/api-docs`
- **Documentation détaillée** : Voir [API_DOCUMENTATION.md](API_DOCUMENTATION.md)

## Endpoints Principaux

### Clients
- `GET /api/clients` - Liste tous les clients
- `POST /api/clients` - Créer un client
- `GET /api/clients/{id}` - Récupérer un client
- `PUT /api/clients/{id}` - Mettre à jour un client
- `DELETE /api/clients/{id}` - Supprimer un client

### Commandes
- `GET /api/commandes` - Liste toutes les commandes
- `POST /api/commandes` - Créer une commande
- `GET /api/commandes/{id}` - Récupérer une commande
- `PATCH /api/commandes/{id}/statut` - Mettre à jour le statut

### Articles
- `GET /api/articles` - Liste des types d'articles et prix

### Dashboard
- `GET /api/dashboard/stats` - Statistiques du tableau de bord

## Structure du Projet

```
src/main/java/com/example/pressing/
├── config/          # Configurations (CORS, Swagger)
├── controller/      # Contrôleurs REST
├── dto/            # Objets de transfert de données
├── entity/         # Entités JPA
├── enumeration/    # Énumérations
├── exception/      # Gestion des exceptions
├── mapper/         # Mappers DTO <-> Entity
├── repository/     # Repositories JPA
└── service/        # Services métier
```

## Tests

```bash
# Exécuter les tests
mvn test
```

## Déploiement

### Créer un JAR exécutable
```bash
mvn clean package
java -jar target/pressing-1.0-SNAPSHOT.jar
```

### Variables d'environnement pour la production
```bash
export SPRING_DATASOURCE_URL=******************************************
export SPRING_DATASOURCE_USERNAME=postgres
export SPRING_DATASOURCE_PASSWORD=your_password
```

## Intégration Frontend

Cette API est conçue pour être utilisée avec un frontend (React, Vue.js, Angular, etc.).

### Configuration CORS
L'API est configurée pour accepter les requêtes cross-origin. En production, modifiez la configuration CORS dans `CorsConfig.java`.

### Exemples d'utilisation JavaScript

```javascript
// Récupérer tous les clients
fetch('http://localhost:8080/api/clients')
  .then(response => response.json())
  .then(clients => console.log(clients));

// Créer un client
fetch('http://localhost:8080/api/clients', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    nom: 'Jean Dupont',
    telephone: '0123456789',
    adresse: '123 Rue de la Paix'
  })
})
.then(response => response.json())
.then(client => console.log(client));
```

## Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajouter nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## Licence

Ce projet est sous licence MIT.
